#include "pch.h"
#include "aimbot.hpp"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"
#include <iostream>

// ===========================
// AIMBOT IMPLEMENTATION
// ===========================

// ===========================
// THREAD MANAGEMENT
// ===========================

void Aimbot::StartAimbotThread(const Reader& reader)
{
	if (threadRunning.load()) {
		std::cout << "[!] Aimbot thread is already running!" << std::endl;
		return;
	}

	shouldStop.store(false);
	threadRunning.store(true);

	// Create detached thread for aimbot
	aimbotThread = std::thread(&Aimbot::ThreadLoop, this, std::ref(reader));
	aimbotThread.detach();

	std::cout << "[+] Aimbot thread started successfully!" << std::endl;
}

void Aimbot::StopAimbotThread()
{
	if (!threadRunning.load()) {
		return;
	}

	shouldStop.store(true);

	// Wait a bit for thread to finish gracefully
	//std::this_thread::sleep_for(std::chrono::milliseconds(50));

	threadRunning.store(false);
	std::cout << "[+] Aimbot thread stopped!" << std::endl;
}

void Aimbot::ThreadLoop(const Reader& reader)
{
	// Wait for complete initialization
	while (!GameVars::getInstance()->initialized()) {
		std::this_thread::sleep_for(std::chrono::milliseconds(100));
	}

	//std::cout << "[+] Aimbot thread initialization complete" << std::endl;

	while (!shouldStop.load()) {
		// Only run aimbot if enabled
		if (globals::Legitbot::enabled) {
			doAimbot(reader);
		}

		// No sleep for maximum responsiveness - user preference
		// Thread runs at maximum speed for best performance
	}

	threadRunning.store(false);
	//std::cout << "[+] Aimbot thread loop ended" << std::endl;
}

void Aimbot::doAimbot(const Reader& reader)
{
	view_matrix_t viewMatrix = GameData::getViewMatrix();
	auto playerList = reader.getPlayerListCopy();

	std::vector<Vector> playerPositions;
	playerPositions.clear();
	
	for (const auto& player : playerList)
	{
		// get the 3D position of the player we're CURRENTLY looping through.
		Vector playerPosition = driver::read_memory<Vector>(GameVars::getInstance()->getDriver(), player.BoneArray + bones::head * 32);

		
		//if (!player.enemySpotted && player.playerTeam != localTeam && Legitbot.visiblecheck)
		//	continue;

		int localTeam = GameData::getLocalTeam();
		if (player.team == localTeam && globals::Legitbot::teamcheck)
			continue;
	

		Vector h;
		if (Vector::world_to_screen(viewMatrix, playerPosition, h))
		{   
			playerPositions.push_back(h);
		}
	}

	if (GetAsyncKeyState(0x58)) // X on keyboard
	{
		auto closest_player = findClosest(playerPositions);
		if (!closest_player.IsZero())
		{
			MoveMouseToPlayer(closest_player);
		}
	}
}

Vector Aimbot::findClosest(const std::vector<Vector> playerPositions)
{
	if (playerPositions.empty()) return Vector{0,0,0};

	Vector center_of_screen{
		static_cast<float>(GetSystemMetrics(SM_CXSCREEN)) / 2,
		static_cast<float>(GetSystemMetrics(SM_CYSCREEN)) / 2,
		0.0f
	};
	

	float max_distance_sq = 5 * globals::Legitbot::radius * globals::Legitbot::radius * 5;
	float closest_distance_sq = FLT_MAX;
	Vector closest = Vector{0,0,0};

	for (const auto& pos : playerPositions) {
		float dx = pos.x - center_of_screen.x;
		float dy = pos.y - center_of_screen.y;
		float distance_sq = dx*dx + dy*dy;

		if (distance_sq < closest_distance_sq && distance_sq < max_distance_sq) {
			closest_distance_sq = distance_sq;
			closest = pos;
		}
	}
	return closest;
}

void Aimbot::MoveMouseToPlayer(Vector position)
{
	if (position.IsZero())
		return;

	// Thread-safe mouse movement
	std::lock_guard<std::mutex> lock(mouseMutex);

	POINT currentMousePos;
	GetCursorPos(&currentMousePos);
	Vector currentPos{
		static_cast<float>(currentMousePos.x),
		static_cast<float>(currentMousePos.y),
		0.0f
	};

	float deltaX = position.x - currentPos.x;
	float deltaY = position.y - currentPos.y;

	// Simple smoothness implementation - user preference: no deadzone, smoothness 1 = exactly 1 step
	if (globals::Legitbot::smoothness <= 1.0f) {
		// Smoothness 1: Move directly to target in one step
		mouse_event(MOUSEEVENTF_MOVE, static_cast<LONG>(std::round(deltaX)),
			static_cast<LONG>(std::round(deltaY)), 0, 0);
		return;
	}

	// For smoothness > 1: Simple division without complex logic
	float stepX = deltaX / globals::Legitbot::smoothness;
	float stepY = deltaY / globals::Legitbot::smoothness;

	// No deadzone - always move if there's any delta
	// No accumulation - keep it simple and direct
	mouse_event(MOUSEEVENTF_MOVE, static_cast<LONG>(std::round(stepX)),
		static_cast<LONG>(std::round(stepY)), 0, 0);
}