#pragma once
#include "../../gamedata.hpp"
#include "../../globals.hpp"
#include "../../bones.hpp"
#include "../../../render/render.hpp"
#include "../../../math/vector.hpp"
#include "../../entity.hpp"

#include <thread>
#include <chrono>
#include <atomic>
#include <mutex>
#include <Windows.h>

class Aimbot
{
public:
	void doAimbot(const Reader& reader);

	// Thread management methods
	void StartAimbotThread(const Reader& reader);
	void StopAimbotThread();
	bool IsThreadRunning() const { return threadRunning.load(); }

private:
	Vector findClosest(const std::vector<Vector> playerPositions);
	void MoveMouseToPlayer(Vector position);
	void ThreadLoop(const Reader& reader);

	// Thread synchronization
	std::atomic<bool> threadRunning{false};
	std::atomic<bool> shouldStop{false};
	std::thread aimbotThread;
	mutable std::mutex mouseMutex;

	// Mouse movement state
	float accumulatedX = 0.0f;
	float accumulatedY = 0.0f;
};

inline Aimbot aimbot;